# SSL证书配置说明

## 目录结构

```
ssl/
├── liangliangdamowang.edu.deal/
│   ├── cert.pem          # SSL证书文件
│   └── key.pem           # 私钥文件
└── dhparam.pem           # DH参数文件
```

## 证书部署步骤

### 1. 放置SSL证书
将你的SSL证书文件放置到对应目录：
- 证书文件：`ssl/liangliangdamowang.edu.deal/cert.pem`
- 私钥文件：`ssl/liangliangdamowang.edu.deal/key.pem`

### 2. 生成DH参数文件
```bash
openssl dhparam -out ssl/dhparam.pem 2048
```

### 3. 设置文件权限
```bash
chmod 600 ssl/liangliangdamowang.edu.deal/key.pem
chmod 644 ssl/liangliangdamowang.edu.deal/cert.pem
chmod 644 ssl/dhparam.pem
```

## 证书格式要求

- 证书文件应为PEM格式
- 如果有中间证书，请将其合并到cert.pem文件中
- 私钥文件不应有密码保护

## 自动续期

如果使用Let's Encrypt，可以配置自动续期：
```bash
# 在crontab中添加
0 2 * * * /usr/bin/certbot renew --quiet && docker-compose -f /root/workspace/shared/nginx/docker-compose.yml exec nginx nginx -s reload
```
