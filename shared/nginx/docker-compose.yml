version: '3.8'

services:
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 配置文件
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./conf.d:/etc/nginx/conf.d:ro
      - ./includes:/etc/nginx/includes:ro

      # SSL证书
      - ./ssl:/etc/nginx/ssl:ro

      # 日志目录
      - ./logs/access:/var/log/nginx/access
      - ./logs/error:/var/log/nginx/error

      # 静态文件（如果有）
      - /var/www/html:/var/www/html:ro

    networks:
      - nginx-network
    depends_on:
      - new-api
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  new-api:
    image: calciumion/new-api:latest
    container_name: new-api
    restart: always
    command: --log-dir /app/logs
    expose:
      - "3000"
    volumes:
      - ../../new-api/data:/data
      - ../../new-api/logs:/app/logs
    environment:
      - SQL_DSN=root:123456@tcp(mysql:3306)/new-api
      - REDIS_CONN_STRING=redis://redis
      - TZ=Asia/Shanghai
      - ERROR_LOG_ENABLED=true
      - BASE_URL=https://liangliangdamowang.edu.deal/ai
      - FRONTEND_BASE_URL=https://liangliangdamowang.edu.deal/ai
    depends_on:
      - redis
      - mysql
    networks:
      - nginx-network
    healthcheck:
      test: ["CMD-SHELL", "wget -q -O - http://localhost:3000/api/status | grep -o '\"success\":\\s*true' | awk -F: '{print $$2}'"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:alpine
    container_name: redis
    restart: always
    networks:
      - nginx-network

  mysql:
    image: mysql:8.2
    container_name: mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: new-api
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - nginx-network

networks:
  nginx-network:
    driver: bridge

volumes:
  mysql_data:
