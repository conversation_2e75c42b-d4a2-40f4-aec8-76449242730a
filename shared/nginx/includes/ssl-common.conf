# SSL通用配置
# 支持的SSL协议版本
ssl_protocols TLSv1.2 TLSv1.3;

# 加密套件配置（推荐Mozilla Modern配置）
ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

# 优先使用服务器端加密套件
ssl_prefer_server_ciphers off;

# SSL会话配置
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 1d;
ssl_session_tickets off;

# DH参数文件
ssl_dhparam /etc/nginx/ssl/dhparam.pem;

# OCSP装订
ssl_stapling on;
ssl_stapling_verify on;
resolver ******* ******* valid=300s;
resolver_timeout 5s;

# SSL缓冲区优化
ssl_buffer_size 1400;

# 预加载HSTS（可选，根据需要启用）
# add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
