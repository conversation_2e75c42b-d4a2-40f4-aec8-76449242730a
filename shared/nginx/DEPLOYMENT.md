# New-API 子路径部署指南

## 部署目标
实现 `https://liangliangdamowang.edu.deal/ai` 访问new-api项目

## 部署步骤

### 1. 准备SSL证书
```bash
# 将你的SSL证书放置到指定位置
cp your-cert.pem ssl/liangliangdamowang.edu.deal/cert.pem
cp your-key.pem ssl/liangliangdamowang.edu.deal/key.pem

# 生成DH参数文件
openssl dhparam -out ssl/dhparam.pem 2048

# 设置权限
chmod 600 ssl/liangliangdamowang.edu.deal/key.pem
chmod 644 ssl/liangliangdamowang.edu.deal/cert.pem
chmod 644 ssl/dhparam.pem
```

### 2. 创建日志目录
```bash
mkdir -p logs/access logs/error
chmod 755 logs/access logs/error
```

### 3. 配置DNS
在你的域名管理面板中添加A记录：
```
类型: A
主机记录: @
记录值: 你的服务器IP
TTL: 600
```

### 4. 启动服务
```bash
# 在 /root/workspace/shared/nginx 目录下执行
docker-compose up -d

# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f nginx
docker-compose logs -f new-api
```

### 5. 验证部署
```bash
# 检查nginx配置
docker-compose exec nginx nginx -t

# 测试HTTP重定向
curl -I http://liangliangdamowang.edu.deal

# 测试HTTPS访问
curl -I https://liangliangdamowang.edu.deal/ai

# 测试API接口
curl https://liangliangdamowang.edu.deal/ai/api/status
```

## 访问路径说明

- **主站**: `https://liangliangdamowang.edu.deal/`
- **New-API**: `https://liangliangdamowang.edu.deal/ai/`
- **API接口**: `https://liangliangdamowang.edu.deal/ai/api/*`
- **健康检查**: `https://liangliangdamowang.edu.deal/health`

## 安全特性

✅ **SSL/TLS加密**: TLS 1.2/1.3 + 强加密套件  
✅ **安全头**: HSTS, CSP, X-Frame-Options等  
✅ **访问控制**: IP限制、恶意请求过滤  
✅ **限流保护**: API限流、连接数限制  
✅ **日志记录**: 完整的访问和错误日志  

## 故障排查

### 常见问题
1. **502 Bad Gateway**: 检查new-api容器是否正常运行
2. **SSL证书错误**: 检查证书文件路径和权限
3. **404错误**: 检查nginx配置中的路径重写规则

### 调试命令
```bash
# 检查容器状态
docker-compose ps

# 查看nginx错误日志
docker-compose logs nginx

# 进入nginx容器调试
docker-compose exec nginx sh

# 测试配置文件
docker-compose exec nginx nginx -t
```
